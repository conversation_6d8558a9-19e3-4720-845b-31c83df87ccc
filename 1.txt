公司
职位：开发 
2023.08 - 2025.02
 1. CSGO道具交易平台： 2023.08-2025.02 用Uniapp搞定多端开发：用Uniapp开发了一套代码，适配了H5网页、
Android和iOS App，省了不少开发时间，还保证了 不同设备上用起来都顺手。 对接Steam API的前端工作：负责前
端跟Steam API的对接，做到了玩家登录授权和道具数据实时显示，界面反应快，数据 也准，玩家用着挺顺畅。 交易
功能页面开发：主导了饰品上架、下架、库存管理和交易的页面开发，用Uniapp搭出好看又好用的界面，哪怕很多玩 
家同时操作，页面也不卡。 优化用户体验：设计了直观易用的交易界面，手机和网页都能适配，加载速度快，玩家用
得爽，留下来交易的也多了。 使用php完成后端的toB api设计及文档编写，涉及：限流，签名认证，通用响应和错误
处理，以及饰品查询，下单，订单查 询等api接口 2.NFT交易平台 React开发H5页面：用React打造了NFT交易平台的
H5网页，代码结构清晰，界面响应快。 对接区块链API：负责前端与区块链API的对接，实现了用户钱包登录、NFT资
产展示和交易数据实时刷新，数据准、页面不 卡。 交易功能页面开发：主导了NFT上架、下架、浏览和交易的核心页
面开发，用React组件化开发。 用户体验优化：设计了简单直观的交易界面，加载速度快，操作方便，适配移动端和
PC端。 
武汉仕耀科技有限公司
职位：开发 
2021.06 - 2023.08
 1.主要负责 ebay和 Shopee 跨界电商 erp 系统的开发与维护 2. 负责日常运营人员需求开发 3. 负责新 Walmart 新跨
境电商平台的接入以及 erp 新模块开发 4. 解决 ebay和 shopee 跨境电商 erp 系统的历史 bug 使用技术： 2021.06
2023.08 1.使用 lavaral 框架进行开发 3.使用 jekins+docker 进行部署 2. 部分功能使用 golang（gin 框架）进行开发 
业绩: 1.解决大日志文件下载慢，且下载中断问题 2. 接入沃尔玛跨境电商平台到本地 erp 系统 
北京华数互动科技有限公司
职位：开发 
2017.09 - 2021.06
 ● 配合运营人员完成相应个业务需求开发 ● 开发维护7k7k网站管理的后台 ● 改版和维护7k7k网站 ● 改版和维护支付 
● 进行日常网站活动开发 使用技术： 1.使用 ci 框架+mysql+redis 2. 使用 jekins 进行项目部署 3. 使用 git 进行本版
维护 4.部分定时任务使用 golang （如 sitemap 推送，缓存自动更新等） 业绩: 1.改版7k7k主站 2. 接入第三方大型网
页游戏 3. 开发抽奖活动 
北京融科汇通技术有限公司
职位：开发 
2015.03 - 2017.05
全权负责PHP项目开发，主要项目是卫计委资金暂存管理系统 主要完成项目需求分析，项目设计， 代码编程， 主要与
客户进行沟通负责解决各种业务问题和编码问题 
项目经历
tgbot代币交易平台
所属公司：(上海月升于海信息技术有限公司) 
项目描述： 
2024.02 - 2025.04
 tgBot项目是一款基于Telegram Bot API开发的数字货币交易自动化系统。该项目专注于Solana区块链生态，实现了
多维度交易管理功能，包括智能买卖策略执行、数字钱包资产管理、多级邀请返佣机制、多语言界面支持以及Gas费智
能优化等核心功能模块。
你的成就： 
负责 Telegram Bot API 的对接与功能开发，主导 Bot 前端项目的架构设计与实现；完成区块链 API 的集成与调用，
并负责前端核心业务逻辑的完整开发与优化
skindeer饰品交易平台
所属公司：(上海月升于海信息技术有限公司) 
项目描述： 
2023.08 - 2025.02
 Skindeer是一个专注于CSGO饰品交易的综合性平台，采用B2C和B2B双端运营模式。在C端业务中，平台通过Web端
和移动端双渠道，为个人用户提供安全高效的饰品交易服务。B端业务主要面向开箱网站、CSGO饰品批发商及其他交
易平台，建立战略合作伙伴关系。平台货源渠道多元化，包括自有用户库存、悠悠有品及C5game等主流交易平台的优
质资源。
你的成就： 
主导企业级API接口设计与开发
负责支付系统核心模块架构与实现
成功集成Stripe支付网关与Tron区块链技术
参与整个项目的开发和维护
7k7k主站
所属公司：(北京华数互动科技有限公司) 
项目描述： 
2020.01 - 2023.08
 7k7k是国内领先的网页游戏聚合平台，为玩家提供数千款精品页游的在线体验。作为核心开发成员，我深度参与了平
台的整体维护、功能迭代及用户体验优化工作，助力平台在竞争激烈的页游市场中保持领先地位。
你的成就： 
● 负责7k7k游戏平台的整体项目维护，确保系统稳定运行，优化性能并提升用户体验。
● 负责7k7k网站核心游戏界面的改版的开发。
● 负责支付模块的日常维护与功能迭代，对接第三方支付接口，保障交易流程的安全性和稳定性。
● 独立设计并开发福利抽奖活动系统，包括活动逻辑实现、数据统计及风控策略，提升用户参与度和平台收益。
老黄历万
所属公司：(北京华数互动科技有限公司) 
项目描述： 
2017.05 - 2021.07
 91老黄历（91cha.com）是国内领先的传统文化资讯聚合平台，专注于为用户提供精准的老黄历查询、生肖运势、八
字测算、风水命理等传统民俗服务。作为核心开发成员，我负责平台的整体架构设计、功能开发及性能优化，助力平台
在垂直领域保持领先地位。
你的成就： 
负责91老黄历核心系统的日常运维，保障平台高可用性
负责支付系统维护
负责项目改版和新功能接入
负责项目新平台开发（小程序，公众号）
教育经历
湖北理工学院   大专 / 应用电子技术