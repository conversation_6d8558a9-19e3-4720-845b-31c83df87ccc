# 部署指南

## 快速部署

### 1. 静态托管平台部署

#### Vercel 部署
1. 将项目推送到 GitHub
2. 访问 [Vercel](https://vercel.com)
3. 导入 GitHub 仓库
4. 自动部署完成

#### Netlify 部署
1. 将项目推送到 GitHub
2. 访问 [Netlify](https://netlify.com)
3. 连接 GitHub 仓库
4. 设置构建命令：`sass assets/css/style.scss assets/css/style.css --no-source-map --style=compressed`
5. 发布目录：`./`

#### GitHub Pages 部署
1. 将项目推送到 GitHub
2. 进入仓库设置 → Pages
3. 选择源分支（通常是 main）
4. 自动部署完成

### 2. 自定义域名配置

#### DNS 设置
```
A记录: @ → 服务器IP
CNAME: www → 主域名
```

#### SSL证书
大多数静态托管平台会自动提供免费SSL证书。

### 3. 性能优化

#### 图片优化
- 使用 WebP 格式
- 压缩图片大小
- 添加 lazy loading

#### CSS/JS 优化
- 压缩 CSS 和 JavaScript
- 启用 Gzip 压缩
- 使用 CDN

### 4. SEO 配置

#### 必要步骤
1. 更新 `sitemap.xml` 中的域名
2. 更新 HTML 文件中的 canonical URL
3. 提交到 Google Search Console
4. 提交到 Bing Webmaster Tools

#### Google Analytics（可选）
在 `<head>` 标签中添加：
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_TRACKING_ID');
</script>
```

### 5. 本地开发

#### 安装依赖
```bash
# 安装 Sass（如果未安装）
npm install -g sass
```

#### 开发命令
```bash
# 编译 SCSS
sass assets/css/style.scss assets/css/style.css --no-source-map --style=compressed

# 监听文件变化
sass --watch assets/css/style.scss:assets/css/style.css --no-source-map --style=compressed

# 本地服务器（可选）
python -m http.server 8000
# 或
npx serve .
```

### 6. 自定化配置

#### 个人信息修改
1. 更新 `index.html` 和 `index-en.html` 中的个人信息
2. 替换联系方式和社交链接
3. 更新项目链接和公司信息

#### 样式自定义
1. 修改 `assets/css/style.scss` 中的颜色变量
2. 重新编译 CSS
3. 测试响应式效果

#### 功能扩展
1. 在 `assets/js/main.js` 中添加新功能
2. 更新 HTML 结构（如需要）
3. 测试兼容性

### 7. 浏览器兼容性

支持的浏览器：
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

### 8. 监控和维护

#### 性能监控
- 使用 Google PageSpeed Insights
- 监控 Core Web Vitals
- 定期检查加载速度

#### 内容更新
- 定期更新工作经历
- 添加新项目经验
- 更新技能列表

### 9. 备份和版本控制

#### Git 工作流
```bash
# 开发分支
git checkout -b feature/update-resume

# 提交更改
git add .
git commit -m "更新简历内容"

# 合并到主分支
git checkout main
git merge feature/update-resume

# 推送到远程
git push origin main
```

#### 备份策略
- 定期备份到多个 Git 平台
- 保存重要版本的快照
- 备份图片和资源文件
