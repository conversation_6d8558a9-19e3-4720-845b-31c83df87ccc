# 项目规划：MResume 个人简历网站

## 1. 项目目标
- 搭建一个极致SEO优化的个人简历静态网站，提升个人曝光度和入职概率。
- 支持多语言（国际化），方便不同国家/地区的访客浏览。
- 页面美观、加载极快、内容结构清晰。

## 2. 技术栈
- HTML5：页面结构与内容。
- SCSS：样式编写，便于维护和扩展。
- jQuery（可选）：如有交互需求时使用，无需则不引入。

## 3. 目录结构规划
```
MResume/
├── index.html           # 默认主页（如中文）
├── index-en.html        # 英文主页
├── assets/
│   ├── css/
│   │   └── style.scss   # SCSS主样式文件
│   ├── js/
│   │   └── main.js      # 可选，包含jQuery相关交互
│   └── images/          # 站点图片、头像等
├── ProjectPlan.md       # 项目规划文档
├── README.md            # 项目说明
└── ...
```

## 4. SEO 优化要点
- **语义化HTML**：使用header、main、footer、section、article等标签。
- **Title/Meta标签**：每个页面独立设置title、description、keywords。
- **多语言SEO**：使用`<link rel="alternate" hreflang="...">`声明多语言页面。
- **结构化数据**：使用JSON-LD或Microdata标注简历信息（如Person、WorkExperience等）。
- **图片优化**：使用alt属性，图片压缩，WebP优先。
- **页面加载速度**：精简JS/CSS，开启Gzip/压缩，图片懒加载。
- **robots.txt/sitemap.xml**：引导搜索引擎抓取。
- **无用JS不引入**：如无交互，jQuery不加载。

## 5. 国际化方案
- 每种语言单独一个HTML文件（如index.html、index-en.html）。
- 通过页面顶部/底部语言切换按钮跳转不同HTML。
- 每个HTML页面都配置对应的SEO标签和hreflang。

## 6. 开发流程
1. 设计内容结构与样式稿。
2. 编写主HTML页面（如index.html），完成中文内容。
3. 编写SCSS样式，编译为CSS。
4. 如有交互需求，编写main.js并按需引入jQuery。
5. 复制index.html为index-en.html，翻译内容，调整SEO标签。
6. 配置hreflang、结构化数据、图片alt等SEO细节。
7. 部署到静态托管平台，配置robots.txt和sitemap.xml。
8. 持续优化内容和SEO表现。

## 7. 其他建议
- 可用工具：VSCode、Sass CLI、ImageOptim、Google Search Console。
- 推荐部署平台：Vercel、Netlify、GitHub Pages。 