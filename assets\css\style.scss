// 现代专业个人简历样式

// 主题色彩
$color-primary: #2563eb;
$color-secondary: #7c3aed;
$color-accent: #06b6d4;
$color-success: #10b981;
$color-warning: #f59e0b;
$color-danger: #ef4444;
$color-bg: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
$color-bg-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
$color-card: rgba(255,255,255,0.85);
$color-card-dark: rgba(30,41,59,0.95);
$color-title: #1e293b;
$color-title-dark: #f1f5f9;
$color-text: #475569;
$color-text-dark: #cbd5e1;
$color-muted: #64748b;
$color-muted-dark: #94a3b8;
$shadow: 0 10px 25px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
$shadow-lg: 0 25px 50px -12px rgba(0,0,0,0.25);
$radius: 16px;
$radius-lg: 24px;
$transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
$font-main: 'Inter', 'SF Pro Display', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', system-ui, sans-serif;

// 基础样式
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  background: $color-bg;
  min-height: 100%;
  scroll-behavior: smooth;
}

body {
  font-family: $font-main;
  color: $color-text;
  background: transparent;
  margin: 0;
  padding: 20px;
  min-height: 100vh;
  line-height: 1.6;
  transition: $transition;
}

// 暗色主题
@media (prefers-color-scheme: dark) {
  html {
    background: $color-bg-dark;
  }
  body {
    color: $color-text-dark;
  }
  .resume-container, header, footer, .section, .job, .project, .education-item {
    background: $color-card-dark !important;
    color: $color-text-dark !important;
    border-color: rgba(148, 163, 184, 0.2) !important;
  }
  h1, h2, h3, h4 {
    color: $color-title-dark !important;
  }
  .company, .project-title {
    color: $color-accent !important;
  }
}

// 容器样式
.resume-container {
  max-width: 1200px;
  margin: 0 auto;
  background: $color-card;
  border-radius: $radius-lg;
  box-shadow: $shadow-lg;
  overflow: hidden;
  transition: $transition;
}

// 头部样式
header {
  background: linear-gradient(135deg, $color-primary 0%, $color-secondary 100%);
  color: white;
  padding: 3rem 2rem;
  text-align: center;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
  }

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    letter-spacing: -0.02em;
    position: relative;
    z-index: 1;
  }

  .subtitle {
    font-size: 1.1rem;
    margin: 0 0 1.5rem 0;
    opacity: 0.9;
    font-weight: 400;
    position: relative;
    z-index: 1;
  }

  .contact-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    position: relative;
    z-index: 1;

    span {
      font-size: 0.9rem;
      opacity: 0.9;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
  }
}

// 章节样式
.section {
  padding: 2rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);

  &:last-child {
    border-bottom: none;
  }

  h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin: 0 0 1.5rem 0;
    color: $color-title;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &::before {
      content: '';
      width: 4px;
      height: 1.5rem;
      background: linear-gradient(135deg, $color-primary 0%, $color-accent 100%);
      border-radius: 2px;
    }
  }
}

// 技能网格
.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.skill-category {
  background: rgba(37, 99, 235, 0.05);
  border: 1px solid rgba(37, 99, 235, 0.1);
  border-radius: $radius;
  padding: 1.5rem;
  transition: $transition;

  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow;
    border-color: $color-primary;
  }

  h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
    color: $color-primary;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      padding: 0.5rem 0;
      border-bottom: 1px solid rgba(148, 163, 184, 0.1);
      font-size: 0.9rem;

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

.card, section > article {
  background: $color-card;
  border-radius: $radius;
  box-shadow: $shadow;
  padding: 2.2rem 1.7rem 1.7rem 1.7rem;
  margin-bottom: 1.7rem;
  transition: transform 0.2s, box-shadow 0.2s, background $transition;
  position: relative;
  backdrop-filter: $glass-blur;
  border: 1.5px solid rgba(106,130,251,0.10);
  &:hover {
    transform: translateY(-6px) scale(1.025);
    box-shadow: 0 16px 48px 0 rgba(252,92,125,0.18);
    background: linear-gradient(120deg, #f5faff 60%, #e3ecfa 100%);
    border: 1.5px solid $color-accent;
  }
  h3 {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    color: $color-title;
    span {
      font-size: 1.05rem;
      color: $color-muted;
      font-weight: 400;
    }
  }
  .date {
    font-size: 1rem;
    color: $color-info;
    margin-bottom: 0.7rem;
    font-weight: 500;
  }
  ul {
    margin: 0 0 0 1.2rem;
    padding: 0;
    li {
      margin-bottom: 0.5rem;
      line-height: 1.7;
      position: relative;
      padding-left: 1.2em;
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0.7em;
        width: 0.6em;
        height: 0.6em;
        background: linear-gradient(135deg, $color-main 0%, $color-accent 100%);
        border-radius: 50%;
        box-shadow: 0 2px 8px rgba(106,130,251,0.10);
      }
    }
  }
  p {
    margin: 0.2rem 0 0.7rem 0;
    line-height: 1.7;
  }
}

footer {
  text-align: center;
  padding: 2.2rem 1rem 1.2rem 1rem;
  background: $color-card;
  border-radius: $radius;
  box-shadow: $shadow;
  max-width: 700px;
  margin: 0 auto 2rem auto;
  color: $color-muted;
  font-size: 1.05rem;
  transition: background $transition, color $transition;
  backdrop-filter: $glass-blur;
  border: 1.5px solid rgba(106,130,251,0.10);
  nav {
    margin-top: 0.7rem;
    a {
      color: $color-main;
      text-decoration: none;
      font-weight: 600;
      margin: 0 0.7rem;
      transition: color 0.2s;
      border-bottom: 2px solid transparent;
      &:hover {
        color: $color-accent;
        border-bottom: 2px solid $color-accent;
        text-decoration: none;
      }
    }
  }
}

// 响应式
@media (max-width: 600px) {
  header, footer {
    padding: 1.5rem 0.5rem;
    max-width: 98vw;
  }
  main {
    padding: 0 0.2rem;
  }
  .card, section > article {
    padding: 1.2rem 0.7rem 1rem 0.7rem;
  }
  h1 {
    font-size: 2.1rem;
  }
  h2 {
    font-size: 1.3rem;
  }
}

// 按钮样式
.button, button {
  background: linear-gradient(90deg, $color-main 0%, $color-accent 100%);
  color: #fff;
  border: none;
  border-radius: $radius;
  padding: 0.8rem 1.7rem;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  box-shadow: 0 2px 12px rgba(106,130,251,0.10);
  transition: background 0.2s, transform 0.2s;
  letter-spacing: 0.5px;
  &:hover {
    background: linear-gradient(90deg, $color-accent 0%, $color-main 100%);
    transform: translateY(-2px) scale(1.04);
    box-shadow: 0 4px 16px rgba(252,92,125,0.13);
  }
}
