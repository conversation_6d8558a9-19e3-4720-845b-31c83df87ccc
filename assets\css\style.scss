// 现代专业个人简历样式

// 主题色彩
$color-primary: #2563eb;
$color-secondary: #7c3aed;
$color-accent: #06b6d4;
$color-success: #10b981;
$color-warning: #f59e0b;
$color-danger: #ef4444;
$color-bg: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
$color-bg-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
$color-card: rgba(255,255,255,0.85);
$color-card-dark: rgba(30,41,59,0.95);
$color-title: #1e293b;
$color-title-dark: #f1f5f9;
$color-text: #475569;
$color-text-dark: #cbd5e1;
$color-muted: #64748b;
$color-muted-dark: #94a3b8;
$shadow: 0 10px 25px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
$shadow-lg: 0 25px 50px -12px rgba(0,0,0,0.25);
$radius: 16px;
$radius-lg: 24px;
$transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
$font-main: 'Inter', 'SF Pro Display', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', system-ui, sans-serif;

// 基础样式
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  background: $color-bg;
  min-height: 100%;
  scroll-behavior: smooth;
}

body {
  font-family: $font-main;
  color: $color-text;
  background: transparent;
  margin: 0;
  padding: 20px;
  min-height: 100vh;
  line-height: 1.6;
  transition: $transition;
}

// 暗色主题 - 系统偏好
@media (prefers-color-scheme: dark) {
  html:not([data-theme="light"]) {
    background: $color-bg-dark;
  }
  body {
    color: $color-text-dark;
  }
  .resume-container, header, footer, .section, .job, .project, .education-item {
    background: $color-card-dark !important;
    color: $color-text-dark !important;
    border-color: rgba(148, 163, 184, 0.3) !important;
  }
  h1, h2, h3, h4 {
    color: $color-title-dark !important;
  }
  .company, .project-title {
    color: $color-accent !important;
  }
  .job-content h4 {
    background: rgba(37, 99, 235, 0.2) !important;
    color: #60a5fa !important;
  }
  .achievements h4 {
    background: rgba(124, 58, 237, 0.2) !important;
    color: #a78bfa !important;
  }
  strong {
    color: #60a5fa !important;
  }
}

// 暗色主题 - 手动切换
[data-theme="dark"] {
  html {
    background: $color-bg-dark;
  }
  body {
    color: $color-text-dark;
  }
  .resume-container, header, footer, .section, .job, .project, .education-item {
    background: $color-card-dark !important;
    color: $color-text-dark !important;
    border-color: rgba(148, 163, 184, 0.3) !important;
  }
  h1, h2, h3, h4 {
    color: $color-title-dark !important;
  }
  .company, .project-title {
    color: $color-accent !important;
  }
  .job-content h4 {
    background: rgba(37, 99, 235, 0.2) !important;
    color: #60a5fa !important;
  }
  .achievements h4 {
    background: rgba(124, 58, 237, 0.2) !important;
    color: #a78bfa !important;
  }
  strong {
    color: #60a5fa !important;
  }
}

// 亮色主题 - 手动切换
[data-theme="light"] {
  html {
    background: $color-bg;
  }
  body {
    color: $color-text;
  }
}

// 容器样式
.resume-container {
  max-width: 1200px;
  margin: 0 auto;
  background: $color-card;
  border-radius: $radius-lg;
  box-shadow: $shadow-lg;
  overflow: hidden;
  transition: $transition;
}

// 头部样式
header {
  background: linear-gradient(135deg, $color-primary 0%, $color-secondary 100%);
  color: white;
  padding: 3rem 2rem;
  text-align: center;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
  }

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    letter-spacing: -0.02em;
    position: relative;
    z-index: 1;
  }

  .subtitle {
    font-size: 1.1rem;
    margin: 0 0 1.5rem 0;
    opacity: 0.9;
    font-weight: 400;
    position: relative;
    z-index: 1;
  }

  .contact-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    position: relative;
    z-index: 1;

    span {
      font-size: 0.9rem;
      opacity: 0.9;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
  }
}

// 章节样式
.section {
  padding: 2rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);

  &:last-child {
    border-bottom: none;
  }

  h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin: 0 0 1.5rem 0;
    color: $color-title;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &::before {
      content: '';
      width: 4px;
      height: 1.5rem;
      background: linear-gradient(135deg, $color-primary 0%, $color-accent 100%);
      border-radius: 2px;
    }
  }
}

// 技能网格
.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.skill-category {
  background: rgba(37, 99, 235, 0.05);
  border: 1px solid rgba(37, 99, 235, 0.1);
  border-radius: $radius;
  padding: 1.5rem;
  transition: $transition;

  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow;
    border-color: $color-primary;
  }

  h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
    color: $color-primary;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      padding: 0.5rem 0;
      border-bottom: 1px solid rgba(148, 163, 184, 0.1);
      font-size: 0.9rem;

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

// 工作经历样式
.job {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(148, 163, 184, 0.3);
  border-radius: $radius;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: $transition;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow;
    border-color: $color-primary;
  }

  .job-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;

    .company {
      font-size: 1.2rem;
      font-weight: 600;
      color: $color-primary;
      margin: 0;
    }

    .job-title {
      font-size: 1rem;
      color: $color-muted;
      font-weight: 500;
    }

    .job-date {
      font-size: 0.9rem;
      color: $color-accent;
      font-weight: 500;
      background: rgba(6, 182, 212, 0.1);
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
    }
  }

  .job-content {
    h4 {
      font-size: 1.1rem;
      font-weight: 600;
      color: $color-primary;
      margin: 1rem 0 0.5rem 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      background: rgba(37, 99, 235, 0.1);
      padding: 0.5rem 1rem;
      border-radius: 8px;
      border-left: 4px solid $color-primary;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(148, 163, 184, 0.1);
        position: relative;
        padding-left: 1.5rem;

        &:last-child {
          border-bottom: none;
        }

        &::before {
          content: '▸';
          position: absolute;
          left: 0;
          color: $color-primary;
          font-weight: bold;
        }

        strong {
          color: $color-primary;
          font-weight: 600;
        }
      }
    }
  }
}

// 项目经历样式
.project {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(124, 58, 237, 0.2);
  border-radius: $radius;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: $transition;
  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.08);

  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow;
    border-color: $color-secondary;
  }

  .project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;

    .project-title {
      font-size: 1.2rem;
      font-weight: 600;
      color: $color-secondary;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .project-link {
        font-size: 0.9rem;
        color: $color-accent;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .project-company {
      font-size: 0.9rem;
      color: $color-muted;
      font-weight: 500;
    }

    .project-date {
      font-size: 0.9rem;
      color: $color-secondary;
      font-weight: 500;
      background: rgba(124, 58, 237, 0.1);
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
    }
  }

  .project-content {
    .project-description {
      margin-bottom: 1rem;
      line-height: 1.6;
      color: $color-text;
    }

    .achievements {
      h4 {
        font-size: 1rem;
        font-weight: 600;
        color: $color-secondary;
        margin: 0 0 0.5rem 0;
        background: rgba(124, 58, 237, 0.1);
        padding: 0.4rem 0.8rem;
        border-radius: 6px;
        border-left: 3px solid $color-secondary;
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          padding: 0.5rem 0;
          border-bottom: 1px solid rgba(148, 163, 184, 0.1);
          position: relative;
          padding-left: 1.5rem;

          &:last-child {
            border-bottom: none;
          }

          &::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: $color-success;
            font-weight: bold;
          }
        }
      }
    }
  }
}

// 教育经历样式
.education {
  .education-item {
    background: rgba(16, 185, 129, 0.05);
    border: 1px solid rgba(16, 185, 129, 0.1);
    border-radius: $radius;
    padding: 1.5rem;
    transition: $transition;

    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow;
      border-color: $color-success;
    }

    h3 {
      font-size: 1.2rem;
      font-weight: 600;
      color: $color-success;
      margin: 0 0 0.5rem 0;
    }

    .degree {
      font-size: 1rem;
      color: $color-muted;
      font-weight: 500;
    }

    .education-description {
      margin-top: 0.5rem;
      line-height: 1.6;
      color: $color-text;
    }
  }
}

// 个人优势网格
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.advantage-item {
  background: rgba(245, 158, 11, 0.05);
  border: 1px solid rgba(245, 158, 11, 0.1);
  border-radius: $radius;
  padding: 1.5rem;
  text-align: center;
  transition: $transition;

  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow;
    border-color: $color-warning;
  }

  h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: $color-warning;
    margin: 0 0 0.5rem 0;
  }

  p {
    margin: 0;
    line-height: 1.6;
    color: $color-text;
    font-size: 0.9rem;
  }
}

// 页脚样式
footer {
  background: linear-gradient(135deg, $color-title 0%, $color-muted 100%);
  color: white;
  padding: 2rem;
  text-align: center;
  margin-top: 2rem;

  .footer-content {
    p {
      margin: 0.5rem 0;
      opacity: 0.9;

      &:first-child {
        font-weight: 600;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  body {
    padding: 10px;
  }

  .resume-container {
    margin: 0;
    border-radius: $radius;
  }

  header {
    padding: 2rem 1rem;

    h1 {
      font-size: 2rem;
    }

    .subtitle {
      font-size: 1rem;
    }

    .contact-info {
      flex-direction: column;
      gap: 1rem;

      span {
        justify-content: center;
      }
    }
  }

  .section {
    padding: 1.5rem 1rem;

    h2 {
      font-size: 1.5rem;
    }
  }

  .skills-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .job-header, .project-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .job, .project, .education-item, .advantage-item, .skill-category {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  header {
    h1 {
      font-size: 1.75rem;
    }

    .subtitle {
      font-size: 0.9rem;
    }
  }

  .section {
    padding: 1rem 0.75rem;

    h2 {
      font-size: 1.25rem;
    }
  }

  .job, .project, .education-item, .advantage-item, .skill-category {
    padding: 0.75rem;
  }
}

// 打印样式
@media print {
  body {
    background: white;
    color: black;
    padding: 0;
  }

  .resume-container {
    box-shadow: none;
    border-radius: 0;
  }

  header {
    background: white !important;
    color: black !important;

    &::before {
      display: none;
    }
  }

  .section {
    border-bottom: 1px solid #ccc;
    page-break-inside: avoid;
  }

  .job, .project {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  footer {
    background: white !important;
    color: black !important;
  }

  a {
    color: black !important;
    text-decoration: none !important;
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section {
  animation: fadeInUp 0.6s ease-out;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(37, 99, 235, 0.3);
  border-radius: 4px;

  &:hover {
    background: rgba(37, 99, 235, 0.5);
  }
}
