<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="全栈开发工程师，10年+互联网开发经验，精通PHP、Golang、React、Uniapp等技术栈，擅长跨境电商ERP、游戏平台、区块链交易平台开发">
  <meta name="keywords" content="全栈开发,PHP开发,Golang开发,React开发,Uniapp开发,跨境电商,区块链,游戏平台,ERP系统">
  <meta name="author" content="全栈开发工程师">
  <meta property="og:title" content="全栈开发工程师简历 - 10年+互联网开发经验">
  <meta property="og:description" content="全栈开发工程师，10年+互联网开发经验，精通PHP、Golang、React、Uniapp等技术栈，擅长跨境电商ERP、游戏平台、区块链交易平台开发">
  <meta property="og:type" content="profile">
  <meta property="og:url" content="https://yoursite.com/">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="全栈开发工程师简历 - 10年+互联网开发经验">
  <meta name="twitter:description" content="全栈开发工程师，10年+互联网开发经验，精通PHP、Golang、React、Uniapp等技术栈">
  <title>全栈开发工程师简历 - 10年+互联网开发经验 | MResume</title>
  <link rel="stylesheet" href="assets/css/style.css?5">
  <link rel="alternate" hreflang="en" href="index-en.html">
  <link rel="canonical" href="https://yoursite.com/">
  <link rel="manifest" href="manifest.json">
  <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
  <link rel="apple-touch-icon" href="assets/images/icon-192.png">
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "全栈开发工程师",
    "jobTitle": "全栈开发工程师",
    "description": "10年+互联网开发经验，精通PHP、Golang、React、Uniapp等技术栈",
    "url": "https://yoursite.com/",
    "sameAs": [],
    "worksFor": {
      "@type": "Organization",
      "name": "上海月升于海信息技术有限公司"
    },
    "alumniOf": {
      "@type": "EducationalOrganization",
      "name": "湖北理工学院"
    },
    "knowsAbout": ["PHP", "Golang", "React", "Uniapp", "JavaScript", "MySQL", "Redis", "Docker", "Jenkins"]
  }
  </script>
</head>
<body>
  <div class="resume-container">
    <header>
      <h1>全栈开发工程师</h1>
      <p class="subtitle">10年+互联网开发经验 | 精通多技术栈 | 专注商业项目落地</p>
      <div class="contact-info">
        <span>📧 邮箱：<EMAIL></span>
        <span>� 微信：your-wechat-id</span>
        <span>🌐 GitHub：github.com/yourname</span>
      </div>
    </header>

    <section class="section">
      <h2>🎯 个人简介</h2>
      <p>拥有10年+互联网行业全栈开发经验，精通PHP、Golang、React、Uniapp等主流技术栈。曾在多家知名互联网公司担任核心开发角色，主导过跨境电商ERP系统、游戏平台、区块链交易平台、NFT交易平台等多个大型商业项目的设计与开发。具备从需求分析、架构设计、开发实现到部署运维的全流程能力，能够独立承担复杂项目的技术攻关与团队协作。</p>
    </section>

    <section class="section">
      <h2>💪 核心技能</h2>
      <div class="skills-grid">
        <div class="skill-category">
          <h3>后端开发</h3>
          <ul>
            <li>PHP (Laravel, CI框架)</li>
            <li>Golang (Gin框架)</li>
            <li>MySQL, Redis, MongoDB</li>
            <li>RESTful API设计</li>
          </ul>
        </div>
        <div class="skill-category">
          <h3>前端开发</h3>
          <ul>
            <li>React, Vue.js</li>
            <li>Uniapp多端开发</li>
            <li>HTML5, CSS3, JavaScript</li>
            <li>响应式设计</li>
          </ul>
        </div>
        <div class="skill-category">
          <h3>运维部署</h3>
          <ul>
            <li>Docker容器化</li>
            <li>Jenkins CI/CD</li>
            <li>Git版本控制</li>
            <li>Linux服务器管理</li>
          </ul>
        </div>
        <div class="skill-category">
          <h3>业务领域</h3>
          <ul>
            <li>跨境电商ERP</li>
            <li>支付系统集成</li>
            <li>区块链应用</li>
            <li>游戏聚合平台</li>
          </ul>
        </div>
      </div>
    </section>
    <section class="section">
      <h2>💼 工作经历</h2>
      <div class="job">
        <div class="job-header">
          <h3 class="company">上海月升于海信息技术有限公司</h3>
          <span class="job-title">开发工程师</span>
          <span class="job-date">2023.08 - 2025.02</span>
        </div>
        <div class="job-content">
          <h4>🎮 CSGO道具交易平台 (2023.08-2025.02)</h4>
          <ul>
            <li><strong>多端开发：</strong>使用Uniapp开发一套代码适配H5网页、Android和iOS App，提高开发效率，保证跨平台用户体验一致性</li>
            <li><strong>API对接：</strong>负责前端与Steam API的对接，实现玩家登录授权和道具数据实时显示，界面响应快速，数据准确</li>
            <li><strong>交易功能：</strong>主导饰品上架、下架、库存管理和交易页面开发，使用Uniapp构建高性能界面，支持高并发操作</li>
            <li><strong>用户体验：</strong>设计直观易用的交易界面，适配移动端和PC端，优化加载速度，提升用户留存率</li>
            <li><strong>后端API：</strong>使用PHP完成toB API设计及文档编写，包括限流、签名认证、通用响应和错误处理、饰品查询、下单、订单查询等接口</li>
          </ul>

          <h4>🎨 NFT交易平台</h4>
          <ul>
            <li><strong>React开发：</strong>使用React构建NFT交易平台H5页面，代码结构清晰，界面响应快速</li>
            <li><strong>区块链对接：</strong>负责前端与区块链API对接，实现用户钱包登录、NFT资产展示和交易数据实时刷新</li>
            <li><strong>核心功能：</strong>主导NFT上架、下架、浏览和交易核心页面开发，采用React组件化开发模式</li>
            <li><strong>体验优化：</strong>设计简单直观的交易界面，加载速度快，操作便捷，完美适配移动端和PC端</li>
          </ul>
        </div>
      </div>

      <div class="job">
        <div class="job-header">
          <h3 class="company">武汉仕耀科技有限公司</h3>
          <span class="job-title">开发工程师</span>
          <span class="job-date">2021.06 - 2023.08</span>
        </div>
        <div class="job-content">
          <ul>
            <li><strong>跨境电商ERP：</strong>主要负责eBay和Shopee跨境电商ERP系统的开发与维护</li>
            <li><strong>需求开发：</strong>负责日常运营人员需求开发，快速响应业务变化</li>
            <li><strong>平台接入：</strong>负责新Walmart跨境电商平台的接入以及ERP新模块开发</li>
            <li><strong>问题解决：</strong>解决eBay和Shopee跨境电商ERP系统的历史bug，提升系统稳定性</li>
            <li><strong>技术栈：</strong>使用Laravel框架进行开发，部分功能使用Golang(Gin框架)，Jenkins+Docker进行部署</li>
            <li><strong>核心业绩：</strong>解决大日志文件下载慢及下载中断问题；成功接入沃尔玛跨境电商平台到本地ERP系统</li>
          </ul>
        </div>
      </div>

      <div class="job">
        <div class="job-header">
          <h3 class="company">北京华数互动科技有限公司</h3>
          <span class="job-title">开发工程师</span>
          <span class="job-date">2017.09 - 2021.06</span>
        </div>
        <div class="job-content">
          <ul>
            <li><strong>业务需求：</strong>配合运营人员完成相应业务需求开发</li>
            <li><strong>后台管理：</strong>开发维护7k7k网站管理后台系统</li>
            <li><strong>网站维护：</strong>改版和维护7k7k网站及支付系统</li>
            <li><strong>活动开发：</strong>进行日常网站活动开发，提升用户参与度</li>
            <li><strong>技术栈：</strong>使用CI框架+MySQL+Redis，Jenkins进行项目部署，Git进行版本维护</li>
            <li><strong>定时任务：</strong>部分定时任务使用Golang开发(如sitemap推送，缓存自动更新等)</li>
            <li><strong>核心业绩：</strong>改版7k7k主站；接入第三方大型网页游戏；开发抽奖活动系统</li>
          </ul>
        </div>
      </div>

      <div class="job">
        <div class="job-header">
          <h3 class="company">北京融科汇通技术有限公司</h3>
          <span class="job-title">PHP开发工程师</span>
          <span class="job-date">2015.03 - 2017.05</span>
        </div>
        <div class="job-content">
          <ul>
            <li><strong>项目负责：</strong>全权负责PHP项目开发，主要项目是卫计委资金暂存管理系统</li>
            <li><strong>全流程参与：</strong>完成项目需求分析、项目设计、代码编程</li>
            <li><strong>客户沟通：</strong>主要与客户进行沟通，负责解决各种业务问题和编码问题</li>
          </ul>
        </div>
      </div>
    </section>
    <section class="section">
      <h2>🚀 重点项目经历</h2>

      <div class="project">
        <div class="project-header">
          <h3 class="project-title">TgBot代币交易平台</h3>
          <span class="project-company">上海月升于海信息技术有限公司</span>
          <span class="project-date">2024.02 - 2025.04</span>
        </div>
        <div class="project-content">
          <p class="project-description">
            基于Telegram Bot API开发的数字货币交易自动化系统，专注于Solana区块链生态，实现了多维度交易管理功能，包括智能买卖策略执行、数字钱包资产管理、多级邀请返佣机制、多语言界面支持以及Gas费智能优化等核心功能模块。
          </p>
          <div class="achievements">
            <h4>核心成就：</h4>
            <ul>
              <li>负责Telegram Bot API的对接与功能开发，主导Bot前端项目的架构设计与实现</li>
              <li>完成区块链API的集成与调用，并负责前端核心业务逻辑的完整开发与优化</li>
              <li>实现智能交易策略、多级返佣系统、Gas费优化等核心功能</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="project">
        <div class="project-header">
          <h3 class="project-title">Skindeer饰品交易平台 <a href="https://www.skindeer.com/market?lang=cn" target="_blank" rel="noopener" class="project-link">🔗 官网</a></h3>
          <span class="project-company">上海月升于海信息技术有限公司</span>
          <span class="project-date">2023.08 - 2025.02</span>
        </div>
        <div class="project-content">
          <p class="project-description">
            专注于CSGO饰品交易的综合性平台，采用B2C和B2B双端运营模式。在C端业务中，平台通过Web端和移动端双渠道，为个人用户提供安全高效的饰品交易服务。B端业务主要面向开箱网站、CSGO饰品批发商及其他交易平台，建立战略合作伙伴关系。
          </p>
          <div class="achievements">
            <h4>核心成就：</h4>
            <ul>
              <li>主导企业级API接口设计与开发</li>
              <li>负责支付系统核心模块架构与实现</li>
              <li>成功集成Stripe支付网关与Tron区块链技术</li>
              <li>参与整个项目的开发和维护，确保平台稳定运行</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="project">
        <div class="project-header">
          <h3 class="project-title">7k7k主站 <a href="https://www.7k7k.com/" target="_blank" rel="noopener" class="project-link">🔗 官网</a></h3>
          <span class="project-company">北京华数互动科技有限公司</span>
          <span class="project-date">2020.01 - 2023.08</span>
        </div>
        <div class="project-content">
          <p class="project-description">
            国内领先的网页游戏聚合平台，为玩家提供数千款精品页游的在线体验。作为核心开发成员，深度参与了平台的整体维护、功能迭代及用户体验优化工作，助力平台在竞争激烈的页游市场中保持领先地位。
          </p>
          <div class="achievements">
            <h4>核心成就：</h4>
            <ul>
              <li>负责7k7k游戏平台的整体项目维护，确保系统稳定运行，优化性能并提升用户体验</li>
              <li>负责7k7k网站核心游戏界面的改版开发</li>
              <li>负责支付模块的日常维护与功能迭代，对接第三方支付接口，保障交易流程的安全性和稳定性</li>
              <li>独立设计并开发福利抽奖活动系统，包括活动逻辑实现、数据统计及风控策略</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="project">
        <div class="project-header">
          <h3 class="project-title">91老黄历 <a href="https://www.91cha.com" target="_blank" rel="noopener" class="project-link">🔗 官网</a></h3>
          <span class="project-company">北京华数互动科技有限公司</span>
          <span class="project-date">2017.05 - 2021.07</span>
        </div>
        <div class="project-content">
          <p class="project-description">
            国内领先的传统文化资讯聚合平台，专注于为用户提供精准的老黄历查询、生肖运势、八字测算、风水命理等传统民俗服务。作为核心开发成员，负责平台的整体架构设计、功能开发及性能优化。
          </p>
          <div class="achievements">
            <h4>核心成就：</h4>
            <ul>
              <li>负责91老黄历核心系统的日常运维，保障平台高可用性</li>
              <li>负责支付系统维护，确保交易安全稳定</li>
              <li>负责项目改版和新功能接入，提升用户体验</li>
              <li>负责项目新平台开发（小程序，公众号），扩大用户覆盖面</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <section class="section">
      <h2>🎓 教育经历</h2>
      <div class="education">
        <div class="education-item">
          <h3>湖北理工学院</h3>
          <span class="degree">大专 / 应用电子技术</span>
          <p class="education-description">专业课程涵盖电子技术基础、计算机原理、程序设计等，为后续从事软件开发工作奠定了扎实的技术基础。</p>
        </div>
      </div>
    </section>

    <section class="section">
      <h2>🏆 个人优势</h2>
      <div class="advantages-grid">
        <div class="advantage-item">
          <h3>🔧 技术全栈</h3>
          <p>精通PHP、Golang后端开发，熟练掌握React、Uniapp前端技术，具备从前端到后端的全栈开发能力</p>
        </div>
        <div class="advantage-item">
          <h3>🚀 项目经验</h3>
          <p>10年+互联网项目开发经验，参与过跨境电商、游戏平台、区块链等多个领域的大型商业项目</p>
        </div>
        <div class="advantage-item">
          <h3>⚡ 性能优化</h3>
          <p>具备高并发系统设计经验，熟悉缓存、数据库优化、系统架构设计等性能优化技术</p>
        </div>
        <div class="advantage-item">
          <h3>🔄 持续学习</h3>
          <p>紧跟技术发展趋势，具备快速学习新技术的能力，能够将新技术应用到实际项目中</p>
        </div>
      </div>
    </section>
  </div>

  <footer>
    <div class="footer-content">
      <p>© 2024 全栈开发工程师简历. 使用现代Web技术构建</p>
      <p>📧 联系邮箱：<EMAIL> | � 微信：your-wechat-id</p>
    </div>
  </footer>

  <script src="assets/js/main.js"></script>
</body>
</html>
