<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Full-stack Developer with 8+ years of internet development experience, proficient in PHP, Golang, React, Uniapp and other tech stacks, specializing in cross-border e-commerce ERP, gaming platforms, blockchain trading platforms">
  <meta name="keywords" content="Full-stack Developer,PHP Developer,Golang Developer,React Developer,Uniapp Developer,Cross-border E-commerce,Blockchain,Gaming Platform,ERP System">
  <meta name="author" content="Full-stack Developer">
  <meta property="og:title" content="Full-stack Developer Resume - 8+ Years Internet Development Experience">
  <meta property="og:description" content="Full-stack Developer with 8+ years of internet development experience, proficient in PHP, Golang, React, Uniapp and other tech stacks">
  <meta property="og:type" content="profile">
  <meta property="og:url" content="https://yoursite.com/index-en.html">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="Full-stack Developer Resume - 8+ Years Internet Development Experience">
  <meta name="twitter:description" content="Full-stack Developer with 8+ years of internet development experience, proficient in PHP, Golang, React, Uniapp">
  <title>Full-stack Developer Resume - 8+ Years Internet Development Experience | MResume</title>
  <link rel="stylesheet" href="assets/css/style.css?1">
  <link rel="alternate" hreflang="zh-CN" href="index.html">
  <link rel="canonical" href="https://yoursite.com/index-en.html">
  <link rel="manifest" href="manifest.json">
  <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
  <link rel="apple-touch-icon" href="assets/images/icon-192.png">
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "Full-stack Developer",
    "jobTitle": "Full-stack Developer",
    "description": "8+ years of internet development experience, proficient in PHP, Golang, React, Uniapp and other tech stacks",
    "url": "https://yoursite.com/index-en.html",
    "sameAs": [],
    "worksFor": {
      "@type": "Organization",
      "name": "Shanghai Yuesheng Yuhai Information Technology Co., Ltd."
    },
    "alumniOf": {
      "@type": "EducationalOrganization",
      "name": "Hubei Polytechnic University"
    },
    "knowsAbout": ["PHP", "Golang", "React", "Uniapp", "JavaScript", "MySQL", "Redis", "Docker", "Jenkins"]
  }
  </script>
</head>
<body>
  <div class="resume-container">
    <header>
      <h1>Full-stack Developer</h1>
      <p class="subtitle">8+ Years Internet Development Experience | Multi-tech Stack Expert | Focus on Commercial Project Implementation</p>
      <div class="contact-info">
        <span>📧 Email: <EMAIL></span>
        <span>💬 WeChat: your-wechat-id</span>
        <span>🌐 GitHub: github.com/yourname</span>
      </div>
    </header>

    <section class="section">
      <h2>🎯 Professional Summary</h2>
      <p>Experienced full-stack developer with 8+ years in the internet industry, proficient in PHP, Golang, React, Uniapp and other mainstream technology stacks. Served as core developer in multiple renowned internet companies, leading the design and development of large-scale commercial projects including cross-border e-commerce ERP systems, gaming platforms, blockchain trading platforms, and NFT trading platforms. Possess comprehensive capabilities from requirement analysis, architecture design, development implementation to deployment and maintenance, capable of independently handling complex project technical challenges and team collaboration.</p>
    </section>

    <section class="section">
      <h2>💪 Core Skills</h2>
      <div class="skills-grid">
        <div class="skill-category">
          <h3>Backend Development</h3>
          <ul>
            <li>PHP (Laravel, CI Framework)</li>
            <li>Golang (Gin Framework)</li>
            <li>MySQL, Redis, MongoDB</li>
            <li>RESTful API Design</li>
          </ul>
        </div>
        <div class="skill-category">
          <h3>Frontend Development</h3>
          <ul>
            <li>React, Vue.js</li>
            <li>Uniapp Multi-platform Development</li>
            <li>HTML5, CSS3, JavaScript</li>
            <li>Responsive Design</li>
          </ul>
        </div>
        <div class="skill-category">
          <h3>DevOps & Deployment</h3>
          <ul>
            <li>Docker Containerization</li>
            <li>Jenkins CI/CD</li>
            <li>Git Version Control</li>
            <li>Linux Server Management</li>
          </ul>
        </div>
        <div class="skill-category">
          <h3>Business Domains</h3>
          <ul>
            <li>Cross-border E-commerce ERP</li>
            <li>Payment System Integration</li>
            <li>Blockchain Applications</li>
            <li>Gaming Platform Development</li>
          </ul>
        </div>
      </div>
    </section>

    <section class="section">
      <h2>💼 Work Experience</h2>
      <div class="job">
        <div class="job-header">
          <h3 class="company">Shanghai Yuesheng Yuhai Information Technology Co., Ltd.</h3>
          <span class="job-title">Software Developer</span>
          <span class="job-date">2023.08 - 2025.02</span>
        </div>
        <div class="job-content">
          <h4>🎮 CSGO Item Trading Platform (2023.08-2025.02)</h4>
          <ul>
            <li><strong>Multi-platform Development:</strong> Used Uniapp to develop one codebase for H5 web, Android and iOS apps, improving development efficiency and ensuring consistent cross-platform user experience</li>
            <li><strong>API Integration:</strong> Responsible for frontend integration with Steam API, implementing player login authorization and real-time item data display with fast interface response and accurate data</li>
            <li><strong>Trading Features:</strong> Led development of item listing, delisting, inventory management and trading pages using Uniapp, building high-performance interfaces supporting high-concurrency operations</li>
            <li><strong>User Experience:</strong> Designed intuitive trading interfaces compatible with mobile and PC, optimized loading speed, improved user retention rate</li>
            <li><strong>Backend API:</strong> Used PHP to complete toB API design and documentation, including rate limiting, signature authentication, unified response and error handling, item queries, orders, order queries and other API interfaces</li>
          </ul>

          <h4>🎨 NFT Trading Platform</h4>
          <ul>
            <li><strong>React Development:</strong> Built NFT trading platform H5 pages using React with clear code structure and fast interface response</li>
            <li><strong>Blockchain Integration:</strong> Responsible for frontend integration with blockchain APIs, implementing user wallet login, NFT asset display and real-time trading data refresh</li>
            <li><strong>Core Features:</strong> Led development of NFT listing, delisting, browsing and trading core pages using React component-based development</li>
            <li><strong>Experience Optimization:</strong> Designed simple and intuitive trading interface with fast loading, convenient operation, perfect compatibility with mobile and PC</li>
          </ul>
        </div>
      </div>

      <div class="job">
        <div class="job-header">
          <h3 class="company">Wuhan Shiyao Technology Co., Ltd.</h3>
          <span class="job-title">Software Developer</span>
          <span class="job-date">2021.06 - 2023.08</span>
        </div>
        <div class="job-content">
          <ul>
            <li><strong>Cross-border E-commerce ERP:</strong> Mainly responsible for development and maintenance of eBay and Shopee cross-border e-commerce ERP systems</li>
            <li><strong>Requirements Development:</strong> Responsible for daily operational personnel requirements development, quickly responding to business changes</li>
            <li><strong>Platform Integration:</strong> Responsible for new Walmart cross-border e-commerce platform integration and ERP new module development</li>
            <li><strong>Problem Solving:</strong> Solved historical bugs in eBay and Shopee cross-border e-commerce ERP systems, improving system stability</li>
            <li><strong>Tech Stack:</strong> Used Laravel framework for development, some features using Golang (Gin framework), Jenkins+Docker for deployment</li>
            <li><strong>Key Achievements:</strong> Solved large log file slow download and download interruption issues; Successfully integrated Walmart cross-border e-commerce platform into local ERP system</li>
          </ul>
        </div>
      </div>

      <div class="job">
        <div class="job-header">
          <h3 class="company">Beijing Huashu Interactive Technology Co., Ltd.</h3>
          <span class="job-title">Software Developer</span>
          <span class="job-date">2017.09 - 2021.06</span>
        </div>
        <div class="job-content">
          <ul>
            <li><strong>Business Requirements:</strong> Collaborated with operations team to complete corresponding business requirements development</li>
            <li><strong>Backend Management:</strong> Developed and maintained 7k7k website management backend system</li>
            <li><strong>Website Maintenance:</strong> Redesigned and maintained 7k7k website and payment system</li>
            <li><strong>Activity Development:</strong> Conducted daily website activity development to improve user engagement</li>
            <li><strong>Tech Stack:</strong> Used CI framework + MySQL + Redis, Jenkins for project deployment, Git for version control</li>
            <li><strong>Scheduled Tasks:</strong> Some scheduled tasks developed using Golang (such as sitemap push, cache auto-update, etc.)</li>
            <li><strong>Key Achievements:</strong> Redesigned 7k7k main site; Integrated third-party large-scale web games; Developed lottery activity system</li>
          </ul>
        </div>
      </div>

      <div class="job">
        <div class="job-header">
          <h3 class="company">Beijing Rongke Huitong Technology Co., Ltd.</h3>
          <span class="job-title">PHP Developer</span>
          <span class="job-date">2015.03 - 2017.05</span>
        </div>
        <div class="job-content">
          <ul>
            <li><strong>Project Leadership:</strong> Fully responsible for PHP project development, main project was Health Commission fund custody management system</li>
            <li><strong>Full Process Participation:</strong> Completed project requirement analysis, project design, code programming</li>
            <li><strong>Client Communication:</strong> Mainly communicated with clients, responsible for solving various business problems and coding issues</li>
          </ul>
        </div>
      </div>
    </section>

    <section class="section">
      <h2>🚀 Key Project Experience</h2>

      <div class="project">
        <div class="project-header">
          <h3 class="project-title">TgBot Token Trading Platform</h3>
          <span class="project-company">Shanghai Yuesheng Yuhai Information Technology Co., Ltd.</span>
          <span class="project-date">2024.02 - 2025.04</span>
        </div>
        <div class="project-content">
          <p class="project-description">
            A digital currency trading automation system based on Telegram Bot API, focusing on Solana blockchain ecosystem. Implemented multi-dimensional trading management functions including intelligent buy/sell strategy execution, digital wallet asset management, multi-level invitation commission mechanism, multi-language interface support, and Gas fee intelligent optimization.
          </p>
          <div class="achievements">
            <h4>Key Achievements:</h4>
            <ul>
              <li>Responsible for Telegram Bot API integration and feature development, led Bot frontend project architecture design and implementation</li>
              <li>Completed blockchain API integration and calls, responsible for frontend core business logic complete development and optimization</li>
              <li>Implemented intelligent trading strategies, multi-level commission system, Gas fee optimization and other core features</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="project">
        <div class="project-header">
          <h3 class="project-title">Skindeer Item Trading Platform <a href="https://www.skindeer.com/market?lang=cn" target="_blank" rel="noopener" class="project-link">🔗 Website</a></h3>
          <span class="project-company">Shanghai Yuesheng Yuhai Information Technology Co., Ltd.</span>
          <span class="project-date">2023.08 - 2025.02</span>
        </div>
        <div class="project-content">
          <p class="project-description">
            A comprehensive platform focusing on CSGO item trading, adopting B2C and B2B dual-end operation model. In C-end business, the platform provides safe and efficient item trading services for individual users through Web and mobile dual channels. B-end business mainly targets case opening websites, CSGO item wholesalers and other trading platforms to establish strategic partnerships.
          </p>
          <div class="achievements">
            <h4>Key Achievements:</h4>
            <ul>
              <li>Led enterprise-level API interface design and development</li>
              <li>Responsible for payment system core module architecture and implementation</li>
              <li>Successfully integrated Stripe payment gateway and Tron blockchain technology</li>
              <li>Participated in entire project development and maintenance, ensuring platform stable operation</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="project">
        <div class="project-header">
          <h3 class="project-title">7k7k Main Site <a href="https://www.7k7k.com/" target="_blank" rel="noopener" class="project-link">🔗 Website</a></h3>
          <span class="project-company">Beijing Huashu Interactive Technology Co., Ltd.</span>
          <span class="project-date">2020.01 - 2023.08</span>
        </div>
        <div class="project-content">
          <p class="project-description">
            Leading web game aggregation platform in China, providing online experience for thousands of premium web games. As a core development member, deeply participated in platform overall maintenance, feature iteration and user experience optimization, helping the platform maintain leading position in the competitive web game market.
          </p>
          <div class="achievements">
            <h4>Key Achievements:</h4>
            <ul>
              <li>Responsible for 7k7k gaming platform overall project maintenance, ensuring system stable operation, optimizing performance and improving user experience</li>
              <li>Responsible for 7k7k website core gaming interface redesign development</li>
              <li>Responsible for payment module daily maintenance and feature iteration, integrating third-party payment interfaces, ensuring transaction process security and stability</li>
              <li>Independently designed and developed welfare lottery activity system, including activity logic implementation, data statistics and risk control strategies</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="project">
        <div class="project-header">
          <h3 class="project-title">91 Chinese Calendar <a href="https://www.91cha.com" target="_blank" rel="noopener" class="project-link">🔗 Website</a></h3>
          <span class="project-company">Beijing Huashu Interactive Technology Co., Ltd.</span>
          <span class="project-date">2017.05 - 2021.07</span>
        </div>
        <div class="project-content">
          <p class="project-description">
            Leading traditional culture information aggregation platform in China, focusing on providing users with accurate Chinese calendar queries, zodiac fortune, eight-character calculation, feng shui numerology and other traditional folk services. As a core development member, responsible for platform overall architecture design, feature development and performance optimization.
          </p>
          <div class="achievements">
            <h4>Key Achievements:</h4>
            <ul>
              <li>Responsible for 91 Chinese Calendar core system daily operations, ensuring platform high availability</li>
              <li>Responsible for payment system maintenance, ensuring transaction security and stability</li>
              <li>Responsible for project redesign and new feature integration, improving user experience</li>
              <li>Responsible for new platform development (mini-programs, WeChat official accounts), expanding user coverage</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <section class="section">
      <h2>🎓 Education</h2>
      <div class="education">
        <div class="education-item">
          <h3>Hubei Polytechnic University</h3>
          <span class="degree">Associate Degree / Applied Electronic Technology</span>
          <p class="education-description">Professional courses covered electronic technology fundamentals, computer principles, programming design, etc., laying a solid technical foundation for subsequent software development work.</p>
        </div>
      </div>
    </section>

    <section class="section">
      <h2>🏆 Personal Advantages</h2>
      <div class="advantages-grid">
        <div class="advantage-item">
          <h3>🔧 Full-stack Technology</h3>
          <p>Proficient in PHP, Golang backend development, skilled in React, Uniapp frontend technologies, with full-stack development capabilities from frontend to backend</p>
        </div>
        <div class="advantage-item">
          <h3>🚀 Project Experience</h3>
          <p>8+ years of internet project development experience, participated in large-scale commercial projects in multiple domains including cross-border e-commerce, gaming platforms, blockchain</p>
        </div>
        <div class="advantage-item">
          <h3>⚡ Performance Optimization</h3>
          <p>Experience in high-concurrency system design, familiar with caching, database optimization, system architecture design and other performance optimization technologies</p>
        </div>
        <div class="advantage-item">
          <h3>🔄 Continuous Learning</h3>
          <p>Keep up with technology development trends, capable of quickly learning new technologies and applying them to actual projects</p>
        </div>
      </div>
    </section>
  </div>

  <footer>
    <div class="footer-content">
      <p>© 2024 Full-stack Developer Resume. Built with Modern Web Technologies</p>
      <p>📧 Contact Email: <EMAIL> | 💬 WeChat: your-wechat-id</p>
    </div>
  </footer>

  <script src="assets/js/main.js"></script>
</body>
</html>