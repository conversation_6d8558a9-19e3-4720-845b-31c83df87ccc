# MResume 个人简历网站

MResume 是一个极致SEO优化的个人简历静态网站，支持多语言展示，帮助你提升个人曝光度和入职概率。项目采用 HTML + SCSS + （可选 jQuery）实现，专注于内容结构、加载速度和搜索引擎友好性。

## 特性
- 纯静态页面，极致SEO优化
- 支持多语言（如中文、英文）
- 响应式设计，适配多端
- 结构化数据，提升搜索引擎收录
- 可选交互，按需引入jQuery

## 目录结构
```
MResume/
├── index.html           # 默认主页（如中文）
├── index-en.html        # 英文主页
├── assets/
│   ├── css/
│   │   └── style.scss   # SCSS主样式文件
│   ├── js/
│   │   └── main.js      # 可选，包含jQuery相关交互
│   └── images/          # 站点图片、头像等
├── ProjectPlan.md       # 项目规划文档
├── README.md            # 项目说明
└── ...
```

## 开发与使用
1. 克隆本项目到本地
2. 使用Sass编译SCSS为CSS：
   ```bash
   sass assets/css/style.scss assets/css/style.css --no-source-map --style=compressed
   ```
3. 编辑`index.html`和`index-en.html`，完善内容与SEO标签
4. 如需交互，编辑`assets/js/main.js`并按需引入jQuery
5. 本地预览：直接用浏览器打开HTML文件
6. 部署到静态托管平台（如Vercel、Netlify、GitHub Pages）

## SEO与国际化说明
- 每个页面独立设置`<title>`、`<meta name="description">`等标签
- 使用`<link rel="alternate" hreflang="...">`声明多语言页面
- 结构化数据采用JSON-LD或Microdata标注简历信息
- 图片均需设置`alt`属性
- 通过robots.txt和sitemap.xml引导搜索引擎抓取

## 许可证
MIT License